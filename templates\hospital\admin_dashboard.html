{% extends 'hospital/admin_base.html' %}
{% load static %}
{% block content %}
{%include 'hospital/admin_dashboard_cards.html'%}
<br><br><br><br>
<div class="container">
  <div class="row">
    <div class="panel panel-primary col-md-5" style="margin-left:2%;">
      <div class="panel-heading" style="text-align:center;">
        <h6 class="panel-title">Recent Doctors</h6>
      </div>
      <table class="table table-hover" id="dev-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Department</th>
            <th>Mobile</th>
            <th>Status</th>

          </tr>
        </thead>
        {% for d in doctors %}
        <tr>
          <td> {{d.get_name}}</td>
          <td>{{d.department}}</td>
          <td>{{d.mobile}}</td>
          {%if d.status%}
          <td> <span class="label label-primary">Permanent</span></td>
          {% else %}
          <td> <span class="label label-success">On Hold</span></td>
          {% endif %}

        </tr>
        {% endfor %}
      </table>
    </div>

    <div class="panel panel-primary col-md-5" style="margin-left:5%;">
      <div class="panel-heading" style="text-align:center;">
        <h6 class="panel-title">Recent Patient</h6>
      </div>
      <table class="table table-hover" id="dev-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Symptoms</th>
            <th>Mobile</th>
            <th>Address</th>
            <th>Status</th>

          </tr>
        </thead>
        {% for p in patients %}
        <tr>
          <td> {{p.get_name}}</td>
          <td>{{p.symptoms}}</td>
          <td>{{p.mobile}}</td>
          <td>{{p.address}}</td>
          {%if p.status%}
          <td> <span class="label label-primary">Admitted</span></td>
          {% else %}
          <td> <span class="label label-success">On Hold</span></td>
          {% endif %}

        </tr>
        {% endfor %}
      </table>
    </div>
  </div>
</div>

{% endblock content %}
