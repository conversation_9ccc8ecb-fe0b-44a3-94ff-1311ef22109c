{% extends 'hospital/doctor_base.html' %}
{% block content %}
{%load static%}

<head>
  <link href="//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
  <script src="//netdna.bootstrapcdn.com/bootstrap/3.0.0/js/bootstrap.min.js"></script>
  <script src="//code.jquery.com/jquery-1.11.1.min.js"></script>

  <style media="screen">
    a:link {
      text-decoration: none;
    }

    h6 {
      text-align: center;
    }

    .row {
      margin: 100px;
    }
  </style>
</head>
<!--
written By : sumit kumar
facebook : fb.com/sumit.luv
-->
<div class="container">
  <div class="panel panel-primary">
    <div class="panel-heading">
      <h6 class="panel-title">Your Appointments</h6>
    </div>
    <table class="table table-hover" id="dev-table">
      <thead>
        <tr>
          <th>Patient Name</th>
          <th>Picture</th>
          <th>Description</th>
          <th>Mobile</th>
          <th>Address</th>
          <th>Appointment Date</th>
        </tr>
      </thead>
      {% for a,p in appointments %}
      <tr>
        <td>{{a.patientName}}</td>
        <td> <img src="{% static p.profile_pic.url %}" alt="Profile Pic" height="40px" width="40px" /></td>
        <td>{{a.description}}</td>
        <td>{{p.mobile}}</td>
        <td>{{p.address}}</td>
        <td>{{a.appointmentDate}}</td>
      </tr>
      {% endfor %}
    </table>
  </div>
</div>
<!--
developed By : sumit kumar
facebook : fb.com/sumit.luv
youtube : youtube.com/lazycoders
-->

{% endblock content %}
