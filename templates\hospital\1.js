document.getElementById('uploadForm').addEventListener('submit', function(event) {
    event.preventDefault();
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    
    if (file) {
        const formData = new FormData();
        formData.append('document', file);

        fetch('/upload/', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadDocuments();
            }
        });
    }
});

function loadDocuments() {
    fetch('/documents/')
        .then(response => response.json())
        .then(data => {
            const documentsList = document.getElementById('documentsList');
            documentsList.innerHTML = '';
            data.documents.forEach(doc => {
                const link = document.createElement('a');
                link.href = doc.url;
                link.textContent = doc.name;
                link.download = doc.name;
                documentsList.appendChild(link);
                documentsList.appendChild(document.createElement('br'));
            });
        });
}
