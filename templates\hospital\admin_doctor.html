{% extends 'hospital/admin_base.html' %}
{% load static %}
{% block content %}
<br><br>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>
  <link href="http://netdna.bootstrapcdn.com/bootstrap/4.0.0-beta/css/bootstrap.min.css" rel="stylesheet">

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.1/css/all.min.css">

  <style type="text/css">
    a:link {
      text-decoration: none;
    }

    .menu {
      top: 50px;
    }

    h6 {
      color: white;
    }

    .order-card {
      color: #fff;
    }

    .bg-c-blue {
      background: linear-gradient(45deg, #4099ff, #73b4ff);
    }

    .bg-c-green {
      background: linear-gradient(45deg, #2ed8b6, #59e0c5);
    }

    .bg-c-yellow {
      background: linear-gradient(45deg, #FFB64D, #ffcb80);
    }

    .bg-c-pink {
      background: linear-gradient(45deg, #FF5370, #ff869a);
    }


    .card {
      border-radius: 5px;
      -webkit-box-shadow: 0 1px 2.94px 0.06px rgba(4, 26, 55, 0.16);
      box-shadow: 0 1px 2.94px 0.06px rgba(4, 26, 55, 0.16);
      border: none;
      margin-bottom: 30px;
      -webkit-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out;
    }

    .card .card-block {
      padding: 25px;
    }

    .order-card i {
      font-size: 26px;
    }

    .f-left {
      float: left;
    }

    .f-right {
      float: right;
    }
  </style>
</head>

<link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css" rel="stylesheet">
<div class="container">
  <div class="row">
    <div class="col-md-4 col-xl-3">
      <div class="card bg-c-blue order-card">
        <div class="card-block">
          <a href="/admin-view-doctor">
            <h6 class="m-b-20">Doctor Record</h6>
          </a>
          <br>
          <h2 class="text-right"><i class="fas fa-user-nurse f-left"></i></h2>

        </div>
      </div>
    </div>

    <div class="col-md-4 col-xl-3">
      <div class="card bg-c-green order-card">
        <div class="card-block">
          <a href="/admin-add-doctor">
            <h6 class="m-b-20">Register Doctor</h6>
          </a>
          <br>
          <h2 class="text-right"><i class="fas fa-user-plus f-left"></i></h2>
        </div>
      </div>
    </div>

    <div class="col-md-4 col-xl-3">
      <div class="card bg-c-yellow order-card">
        <div class="card-block">
          <a href="/admin-approve-doctor">
            <h6 class="m-b-20">Approve Doctor</h6>
          </a>
          <br>
          <h2 class="text-right"><i class="fas fa-check-circle f-left"></i></h2>
        </div>
      </div>
    </div>

    <div class="col-md-4 col-xl-3">
      <div class="card bg-c-pink order-card">
        <div class="card-block">
          <a href="/admin-view-doctor-specialisation">
            <h6 class="m-b-20">Doctor Specialisation</h6>
          </a>
          <br>
          <h2 class="text-right"><i class="fas fa-user-md f-left"></i></h2>
        </div>
      </div>
    </div>
  </div>
</div>
<!--
  developed By : sumit kumar
  facebook : fb.com/sumit.luv
  youtube : youtube.com/lazycoders
  -->
<br><br><br><br><br><br><br>
{% endblock content %}
