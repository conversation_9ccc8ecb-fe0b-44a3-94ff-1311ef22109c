{% extends 'hospital/doctor_base.html' %}
{% block content %}
{%load static%}

<head>
  <link href="//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
  <script src="//netdna.bootstrapcdn.com/bootstrap/3.0.0/js/bootstrap.min.js"></script>
  <script src="//code.jquery.com/jquery-1.11.1.min.js"></script>

  <style media="screen">
    a:link {
      text-decoration: none;
    }

    h6 {
      text-align: center;
    }

    .row {
      margin: 100px;
    }
  </style>
</head>
<!--
written By : sumit kumar
facebook : fb.com/sumit.luv
-->


<div class="container">

    <form  action="/search" method="get" style="margin-left:70%;" class="form-inline my-2 my-lg-0">
      <input class="form-control mr-sm-2" type="search" placeholder="Search" name="query" id="query" aria-label="Search">
      <button class="btn btn-outline-success my-2 my-sm-0" type="submit">Search</button>
    </form>
 

  {% if patients %}
  <div class="panel panel-primary">
    <div class="panel-heading">
      <h6 class="panel-title">Your Total Patient List</h6>
    </div>
    <table class="table table-hover" id="dev-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Profile Picture</th>
          <th>Symptoms</th>
          <th>Mobile</th>
          <th>Address</th>

        </tr>
      </thead>
      {% for p in patients %}
      <tr>
        <td> {{p.get_name}}</td>
        <td> <img src="{% static p.profile_pic.url %}" alt="Profile Pic" height="40px" width="40px" /></td>
        <td>{{p.symptoms}}</td>
        <td>{{p.mobile}}</td>
        <td>{{p.address}}</td>
      </tr>
      {% endfor %}
    </table>
  </div>
  {%else%}
  <br><br><br>
  <h4 style="text-align: center;color: red;">No Patient Found !!!</h4>
  {% endif %}


</div>
<!--
developed By : sumit kumar
facebook : fb.com/sumit.luv
youtube : youtube.com/lazycoders
-->

{% endblock content %}
