{% extends 'hospital/admin_base.html' %}
{% load widget_tweaks %}
{% block content %}

<head>
  <style media="screen">
    a:link {
      text-decoration: none;
    }

    .note {
      text-align: center;
      height: 80px;
      background: -webkit-linear-gradient(left, #0072ff, #8811c5);
      color: #fff;
      font-weight: bold;
      line-height: 80px;
    }

    .form-content {
      padding: 5%;
      border: 1px solid #ced4da;
      margin-bottom: 2%;
    }

    .form-control {
      border-radius: 1.5rem;
    }

    .btnSubmit {
      border: none;
      border-radius: 1.5rem;
      padding: 1%;
      width: 20%;
      cursor: pointer;
      background: #0062cc;
      color: #fff;
    }

    .menu {
      top: 50px;
    }
  </style>

  <link href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.1/js/bootstrap.min.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>

</head>
<br><br><br>
<!------ signup page for doctor by admin(sumit)  ---------->
<form method="post" enctype="multipart/form-data">
  {% csrf_token %}
  <div class="container register-form">
    <div class="form">
      <div class="note">
        <p>Admit Patient To Hospital</p>
      </div>
      <div class="form-content">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              {% render_field userForm.first_name class="form-control" placeholder="First Name" %}
            </div>
            <div class="form-group">
              {% render_field userForm.username class="form-control" placeholder="Username" %}
            </div>
            <div class="form-group">
              {% render_field patientForm.address class="form-control" placeholder="Address" %}
            </div>
            <div class="form-group">
              {% render_field patientForm.symptoms class="form-control" placeholder="Symptoms" %}
            </div>
            <div class="form-group">
              {% render_field patientForm.profile_pic required="required" class="form-control" placeholder="Profile Picture" %}
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              {% render_field userForm.last_name class="form-control" placeholder="Last Name" %}
            </div>
            <div class="form-group">
              {% render_field userForm.password class="form-control" placeholder="Password" %}
            </div>
            <div class="form-group">
              {% render_field patientForm.mobile  class="form-control" placeholder="Mobile" %}
            </div>
            <div class="form-group">
              {% render_field patientForm.assignedDoctorId class="form-control" placeholder="Doctor" %}
            </div>
          </div>
        </div>
        <button type="submit" class="btnSubmit">Admit</button>
      </div>
    </div>
  </div>

</form>

{% endblock content %}
