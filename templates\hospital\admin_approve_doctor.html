{% extends 'hospital/admin_base.html' %}
{% block content %}
{%load static%}

<head>
  <link href="//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
  <script src="//netdna.bootstrapcdn.com/bootstrap/3.0.0/js/bootstrap.min.js"></script>
  <script src="//code.jquery.com/jquery-1.11.1.min.js"></script>
  <style media="screen">
    a:link {
      text-decoration: none;
    }

    h6 {
      text-align: center;
    }

    .row {
      margin: 100px;
    }
  </style>
</head>
<!--
written By : sumit kumar
facebook : fb.com/sumit.luv
-->
<div class="container">
  <div class="panel panel-primary">
    <div class="panel-heading">
      <h6 class="panel-title">Doctors &nbsp Applied For Registration</h6>
    </div>
    <table class="table table-hover" id="dev-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Profile Picture</th>
          <th>Mobile</th>
          <th>Address</th>
          <th>Department</th>
          <th>Approve</th>
          <th>Reject</th>
        </tr>
      </thead>
      {% for d in doctors %}
      <tr>
        <td> {{d.get_name}}</td>
        <td> <img src="{% static d.profile_pic.url %}" alt="Profile Pic" height="40px" width="40px" /></td>
        <td>{{d.mobile}}</td>
        <td>{{d.address}}</td>
        <td>{{d.department}}</td>
        <td><a class="btn btn-primary btn-xs" href="{% url 'approve-doctor' d.id  %}"><span class="glyphicon glyphicon-ok"></span></a></td>
        <td><a class="btn btn-danger btn-xs" href="{% url 'reject-doctor' d.id  %}"><span class="glyphicon glyphicon-trash"></span></a></td>
      </tr>
      {% endfor %}
    </table>
  </div>


</div>


{% endblock content %}
