<!DOCTYPE html>
<html lang="en" dir="ltr">


<head>
  <meta charset="utf-8">
  <style>
    @page {
      size: A4;
      margin: 1cm;

      @frame footer {
        -pdf-frame-content: footerContent;
        bottom: 0cm;
        margin-left: 9cm;
        margin-right: 9cm;
        height: 1cm;
      }
    }

    .invoice-box {
      max-width: 800px;
      margin: auto;
      padding: 30px;
      border: 1px solid #eee;
      box-shadow: 0 0 10px rgba(0, 0, 0, .15);
      font-size: 16px;
      line-height: 24px;
      font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
      color: #555;
    }

    .invoice-box table {
      width: 100%;
      line-height: inherit;
      text-align: left;
    }

    .invoice-box table td {
      padding: 5px;
      vertical-align: top;
    }

    .invoice-box table tr td:nth-child(2) {
      text-align: right;
    }

    .invoice-box table tr.top table td {
      padding-bottom: 20px;
    }

    .invoice-box table tr.top table td.title {
      font-size: 45px;
      line-height: 45px;
      color: #333;
    }

    .invoice-box table tr.information table td {
      padding-bottom: 40px;
    }

    .invoice-box table tr.heading td {
      background: #eee;
      border-bottom: 1px solid #ddd;
      font-weight: bold;
    }

    .invoice-box table tr.details td {
      padding-bottom: 20px;
    }

    .invoice-box table tr.item td {
      border-bottom: 1px solid #eee;
    }

    .invoice-box table tr.item.last td {
      border-bottom: none;
    }

    .invoice-box table tr.total td:nth-child(2) {
      border-top: 2px solid #eee;
      font-weight: bold;
    }

    @media only screen and (max-width: 600px) {
      .invoice-box table tr.top table td {
        width: 100%;
        display: block;
        text-align: center;
      }

      .invoice-box table tr.information table td {
        width: 100%;
        display: block;
        text-align: center;
      }
    }

    /** RTL **/
    .rtl {
      direction: rtl;
      font-family: Tahoma, 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
    }

    .rtl table {
      text-align: right;
    }

    .rtl table tr td:nth-child(2) {
      text-align: left;
    }
  </style>
</head>

<body>

  <br><br><br>
  <div class="invoice-box">
    <table cellpadding="0" cellspacing="0">
      <tr class="top">
        <td colspan="2">
          <table>
            <tr>
              <td class="title">
                <h5>Hospital Management</h5>
              </td>

              <td>

                Admit Date: {{admitDate}}<br>
                Release Date: {{releaseDate}}<br>
                Days Spent: {{daySpent}}
              </td>
            </tr>
          </table>
        </td>
      </tr>

      <tr class="information">
        <td colspan="2">
          <table>
            <tr>
              <td>
                Patient Name : {{patientName}}<br>
                Patient Mobile : {{mobile}}<br>
                Patient Addres : {{address}}<br>
              </td>

              <td>
                Doctor Name :<br>
                {{assignedDoctorName}}<br>

              </td>
            </tr>
          </table>
        </td>
      </tr>


      <tr class="information">
        <td colspan="2">
          <table>
            <tr>
              <td>
                Disease and Symptoms :<br>
                &nbsp &nbsp &nbsp &nbsp &nbsp {{symptoms}}
              </td>

            </tr>
          </table>
        </td>
      </tr>



      <tr class="information">
        <td colspan="2">
          <table>
            <tr>
              <td>
                Charges :<br><br>
                Room Charge of {{daySpent}} Days : {{roomCharge}}<br>
                Doctor Fee : {{doctorFee}}<br>
                Medicine Cost : {{medicineCost}}<br>
                Other Charge : {{OtherCharge}} <br><br>
                &nbsp &nbsp &nbsp &nbsp &nbsp Total Rupees : {{total}}
              </td>
            </tr>
          </table>
        </td>
      </tr>


    </table>
  </div>
</body>
<!--
developed By : sumit kumar
facebook : fb.com/sumit.luv
youtube : youtube.com/lazycoders
-->

</html>
