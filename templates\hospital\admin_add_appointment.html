{% extends 'hospital/admin_base.html' %}
{% load widget_tweaks %}
{% block content %}

<head>
  <style media="screen">
    a:link {
      text-decoration: none;
    }

    .note {
      text-align: center;
      height: 80px;
      background: -webkit-linear-gradient(left, #0072ff, #8811c5);
      color: #fff;
      font-weight: bold;
      line-height: 80px;
    }

    .form-content {
      padding: 5%;
      border: 1px solid #ced4da;
      margin-bottom: 2%;
    }

    .form-control {
      border-radius: 1.5rem;
    }

    .btnSubmit {
      border: none;
      border-radius: 1.5rem;
      padding: 1%;
      width: 20%;
      cursor: pointer;
      background: #0062cc;
      color: #fff;
    }

    .menu {
      top: 50px;
    }
  </style>

  <link href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.1/js/bootstrap.min.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
</head>
<br><br>
<!------ add appointment page by admin(sumit)  ---------->
<form method="post">
  {% csrf_token %}
  <div class="container register-form">
    <div class="form">
      <div class="note">
        <p>Book Appointment Details</p>
      </div>
      <div class="form-content">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              {% render_field appointmentForm.description class="form-control" placeholder="Description" %}
            </div>
            <div class="form-group">
              {% render_field appointmentForm.doctorId class="form-control" placeholder="doctor" %}
            </div>
            <div class="form-group">
              {% render_field appointmentForm.patientId class="form-control" placeholder="patient" %}
            </div>

          </div>

        </div>
        <button type="submit" class="btnSubmit">Book</button>
      </div>
    </div>
  </div>
</form>

<!--
developed By : sumit kumar
facebook : fb.com/sumit.luv
youtube : youtube.com/lazycoders
-->

{% endblock content %}
